import { Box, useTheme } from "@mui/material";
import React, { memo, useMemo, useEffect, useState } from "react";
import { EditorElement, Track } from "../../types";
import { SnapPoint, SnapType } from "./utils";

interface VisualFeedbackSystemProps {
  isDragging: boolean;
  dragElement?: EditorElement;
  targetTrack?: Track;
  isValidDrop: boolean;
  snapPoint?: SnapPoint;
  dragPosition?: {
    x: number;
    y: number;
  };
  children: React.ReactNode;
}

/**
 * 实时视觉反馈系统组件
 * 提供拖拽时的高亮、阴影、缩放等视觉效果
 */
const VisualFeedbackSystemComponent = ({
  isDragging,
  dragElement,
  targetTrack,
  isValidDrop,
  snapPoint,
  dragPosition,
  children,
}: VisualFeedbackSystemProps) => {
  const theme = useTheme();
  const [feedbackIntensity, setFeedbackIntensity] = useState(0);

  // 根据拖拽状态调整反馈强度
  useEffect(() => {
    if (isDragging) {
      setFeedbackIntensity(1);
    } else {
      const timer = setTimeout(() => setFeedbackIntensity(0), 200);
      return () => clearTimeout(timer);
    }
  }, [isDragging]);

  // 获取反馈颜色主题
  const getFeedbackColors = useMemo(() => {
    if (isValidDrop) {
      return {
        primary: "#4caf50", // 绿色 - 有效放置
        secondary: "rgba(76, 175, 80, 0.2)",
        glow: "rgba(76, 175, 80, 0.6)",
        border: "rgba(76, 175, 80, 0.8)",
      };
    } else if (isDragging && targetTrack) {
      return {
        primary: "#f44336", // 红色 - 无效放置
        secondary: "rgba(244, 67, 54, 0.2)",
        glow: "rgba(244, 67, 54, 0.6)",
        border: "rgba(244, 67, 54, 0.8)",
      };
    } else {
      return {
        primary: "#2196f3", // 蓝色 - 默认拖拽
        secondary: "rgba(33, 150, 243, 0.2)",
        glow: "rgba(33, 150, 243, 0.6)",
        border: "rgba(33, 150, 243, 0.8)",
      };
    }
  }, [isValidDrop, isDragging, targetTrack]);

  // 容器样式 - 全局拖拽状态
  const containerStyles = useMemo(() => ({
    position: "relative",
    width: "100%",
    height: "100%",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    willChange: "transform, filter",
    ...(isDragging && {
      filter: "brightness(0.95) contrast(1.05)",
      transform: "scale(1.001)", // 微妙的缩放效果
    }),
  }), [isDragging]);

  // 拖拽元素高亮样式
  const dragElementStyles = useMemo(() => {
    if (!isDragging || !dragElement) return {};

    return {
      position: "relative",
      zIndex: 1000,
      transform: "scale(1.02) translateZ(0)",
      boxShadow: `
        0 8px 32px ${getFeedbackColors.glow},
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3)
      `,
      border: `2px solid ${getFeedbackColors.border}`,
      borderRadius: "6px",
      backgroundColor: getFeedbackColors.secondary,
      transition: "all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
      willChange: "transform, box-shadow, border-color",
      "&::before": {
        content: '""',
        position: "absolute",
        top: "-4px",
        left: "-4px",
        right: "-4px",
        bottom: "-4px",
        background: `linear-gradient(45deg, ${getFeedbackColors.glow}, transparent, ${getFeedbackColors.glow})`,
        borderRadius: "8px",
        zIndex: -1,
        opacity: 0.5,
        animation: "shimmer 2s infinite",
      },
      "@keyframes shimmer": {
        "0%": { 
          backgroundPosition: "-200% 0",
          opacity: 0.3,
        },
        "50%": { 
          backgroundPosition: "200% 0",
          opacity: 0.7,
        },
        "100%": { 
          backgroundPosition: "-200% 0",
          opacity: 0.3,
        },
      },
    };
  }, [isDragging, dragElement, getFeedbackColors]);

  // 轨道反馈样式
  const trackFeedbackStyles = useMemo(() => {
    if (!isDragging || !targetTrack) return {};

    const baseStyles = {
      position: "relative",
      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      willChange: "background-color, border-color, box-shadow",
    };

    if (isValidDrop) {
      return {
        ...baseStyles,
        backgroundColor: getFeedbackColors.secondary,
        border: `2px dashed ${getFeedbackColors.border}`,
        borderRadius: "6px",
        boxShadow: `inset 0 0 20px ${getFeedbackColors.glow}`,
        animation: "pulseValid 2s infinite",
        "@keyframes pulseValid": {
          "0%": { 
            boxShadow: `inset 0 0 10px ${getFeedbackColors.glow}`,
            borderColor: getFeedbackColors.border,
          },
          "50%": { 
            boxShadow: `inset 0 0 30px ${getFeedbackColors.glow}`,
            borderColor: getFeedbackColors.primary,
          },
          "100%": { 
            boxShadow: `inset 0 0 10px ${getFeedbackColors.glow}`,
            borderColor: getFeedbackColors.border,
          },
        },
      };
    } else {
      return {
        ...baseStyles,
        backgroundColor: getFeedbackColors.secondary,
        border: `2px dashed ${getFeedbackColors.border}`,
        borderRadius: "6px",
        animation: "shakeInvalid 0.5s infinite",
        "@keyframes shakeInvalid": {
          "0%": { transform: "translateX(0)" },
          "25%": { transform: "translateX(-2px)" },
          "75%": { transform: "translateX(2px)" },
          "100%": { transform: "translateX(0)" },
        },
      };
    }
  }, [isDragging, targetTrack, isValidDrop, getFeedbackColors]);

  // 吸附反馈样式
  const snapFeedbackStyles = useMemo(() => {
    if (!snapPoint || !isDragging) return {};

    const getSnapColor = () => {
      switch (snapPoint.type) {
        case SnapType.PLAYHEAD:
          return "#f44336";
        case SnapType.ELEMENT_START:
        case SnapType.ELEMENT_END:
          return "#2196f3";
        case SnapType.TIMELINE_MARKER:
          return "#4caf50";
        case SnapType.GRID_LINE:
          return "#9e9e9e";
        default:
          return "#ff9800";
      }
    };

    const snapColor = getSnapColor();

    return {
      position: "relative",
      "&::after": {
        content: '""',
        position: "absolute",
        top: "-2px",
        left: "-2px",
        right: "-2px",
        bottom: "-2px",
        border: `2px solid ${snapColor}`,
        borderRadius: "8px",
        boxShadow: `0 0 16px ${snapColor}80`,
        animation: "snapPulse 1s infinite",
        zIndex: 1001,
      },
      "@keyframes snapPulse": {
        "0%": { 
          opacity: 0.6,
          transform: "scale(1)",
        },
        "50%": { 
          opacity: 1,
          transform: "scale(1.02)",
        },
        "100%": { 
          opacity: 0.6,
          transform: "scale(1)",
        },
      },
    };
  }, [snapPoint, isDragging]);

  // 鼠标跟随效果
  const mouseFollowStyles = useMemo(() => {
    if (!isDragging || !dragPosition) return { display: "none" };

    return {
      position: "fixed",
      left: `${dragPosition.x}px`,
      top: `${dragPosition.y}px`,
      width: "20px",
      height: "20px",
      borderRadius: "50%",
      backgroundColor: getFeedbackColors.primary,
      boxShadow: `0 0 20px ${getFeedbackColors.glow}`,
      transform: "translate(-50%, -50%)",
      zIndex: 10000,
      pointerEvents: "none",
      animation: "cursorPulse 1.5s infinite",
      "@keyframes cursorPulse": {
        "0%": { 
          opacity: 0.8,
          transform: "translate(-50%, -50%) scale(1)",
        },
        "50%": { 
          opacity: 1,
          transform: "translate(-50%, -50%) scale(1.2)",
        },
        "100%": { 
          opacity: 0.8,
          transform: "translate(-50%, -50%) scale(1)",
        },
      },
    };
  }, [isDragging, dragPosition, getFeedbackColors]);

  return (
    <Box sx={containerStyles}>
      {/* 鼠标跟随指示器 */}
      <Box sx={mouseFollowStyles} />
      
      {/* 主要内容区域 */}
      <Box
        sx={{
          ...dragElementStyles,
          ...trackFeedbackStyles,
          ...snapFeedbackStyles,
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export const VisualFeedbackSystem = memo(
  VisualFeedbackSystemComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.isDragging === nextProps.isDragging &&
      prevProps.dragElement?.id === nextProps.dragElement?.id &&
      prevProps.targetTrack?.id === nextProps.targetTrack?.id &&
      prevProps.isValidDrop === nextProps.isValidDrop &&
      prevProps.snapPoint?.type === nextProps.snapPoint?.type &&
      prevProps.snapPoint?.time === nextProps.snapPoint?.time &&
      prevProps.dragPosition?.x === nextProps.dragPosition?.x &&
      prevProps.dragPosition?.y === nextProps.dragPosition?.y
    );
  }
);

VisualFeedbackSystem.displayName = "VisualFeedbackSystem";
