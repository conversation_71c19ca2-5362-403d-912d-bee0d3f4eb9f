import React, { memo, useCallback, useMemo, useState, useRef, useContext } from "react";
import { Box } from "@mui/material";
import { observer } from "mobx-react";
import { StoreContext } from "../../store";
import { EditorElement, Track } from "../../types";

// 导入我们创建的组件
import { EnhancedSnapIndicator } from "./EnhancedSnapIndicator";
import { DragIndicators } from "./DragIndicators";
import { VisualFeedbackSystem } from "./VisualFeedbackSystem";
import { SmoothDragAnimations } from "./SmoothDragAnimations";
import { PerformanceOptimizedDrag, usePerformanceOptimizedDrag, useDragBoundaries } from "./PerformanceOptimizedDrag";

// 导入增强的工具函数
import { 
  generateEnhancedSnapPoints, 
  findNearestEnhancedSnapPoint, 
  SnapPoint, 
  SnapType,
  EnhancedSnapResult 
} from "./utils";

interface ClipchampStyleTimelineManagerProps {
  track: Track;
  elements: EditorElement[];
  containerWidth: number;
  timelineDisplayDuration: number;
  timelinePanOffsetX: number;
  currentTime: number;
  children: React.ReactNode;
}

/**
 * Clipchamp风格的时间线轨道管理器
 * 集成智能吸附、实时视觉反馈、流畅动画和性能优化
 */
const ClipchampStyleTimelineManagerComponent = ({
  track,
  elements,
  containerWidth,
  timelineDisplayDuration,
  timelinePanOffsetX,
  currentTime,
  children,
}: ClipchampStyleTimelineManagerProps) => {
  const store = useContext(StoreContext);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 拖拽状态
  const [dragState, setDragState] = useState({
    isDragging: false,
    isResizing: false,
    dragElement: null as EditorElement | null,
    dragType: "move" as "move" | "resize-left" | "resize-right",
    targetTrack: null as Track | null,
    isValidDrop: false,
  });

  // 吸附状态
  const [snapState, setSnapState] = useState({
    isSnapped: false,
    snapPoint: null as SnapPoint | null,
    snapResult: null as EnhancedSnapResult | null,
  });

  // 视觉反馈状态
  const [feedbackState, setFeedbackState] = useState({
    insertPosition: null as { x: number; y: number; time: number } | null,
    alignmentLines: null as { vertical: number[]; horizontal: number[] } | null,
    dragPosition: null as { x: number; y: number } | null,
  });

  // 性能优化配置
  const dragConfig = usePerformanceOptimizedDrag({
    throttleMs: 16, // 60fps
    enableAutoScroll: true,
  });

  // 边界检测
  const checkBoundaries = useDragBoundaries(containerRef);

  // 生成增强的吸附点
  const snapPoints = useMemo(() => {
    if (!dragState.isDragging) return [];
    
    return generateEnhancedSnapPoints(
      elements,
      currentTime,
      timelineDisplayDuration,
      dragState.dragElement?.id
    );
  }, [elements, currentTime, timelineDisplayDuration, dragState.isDragging, dragState.dragElement?.id]);

  // 计算时间线位置
  const calculateTimelinePosition = useCallback((time: number): number => {
    const relativeTime = time - timelinePanOffsetX;
    return (relativeTime / timelineDisplayDuration) * containerWidth;
  }, [timelineDisplayDuration, timelinePanOffsetX, containerWidth]);

  // 计算时间从位置
  const calculateTimeFromPosition = useCallback((x: number): number => {
    const relativePosition = x / containerWidth;
    return relativePosition * timelineDisplayDuration + timelinePanOffsetX;
  }, [containerWidth, timelineDisplayDuration, timelinePanOffsetX]);

  // 处理拖拽开始
  const handleDragStart = useCallback((event: React.MouseEvent, element: EditorElement, type: "move" | "resize-left" | "resize-right" = "move") => {
    setDragState({
      isDragging: true,
      isResizing: type !== "move",
      dragElement: element,
      dragType: type,
      targetTrack: track,
      isValidDrop: true,
    });

    setFeedbackState(prev => ({
      ...prev,
      dragPosition: { x: event.clientX, y: event.clientY },
    }));

    // 暂停播放
    if (store.playing) {
      store.setPlaying(false);
    }
  }, [track, store]);

  // 处理拖拽移动
  const handleDragMove = useCallback((event: MouseEvent, deltaX: number, deltaY: number) => {
    if (!dragState.isDragging || !dragState.dragElement) return;

    const currentX = event.clientX;
    const currentY = event.clientY;
    
    // 更新拖拽位置
    setFeedbackState(prev => ({
      ...prev,
      dragPosition: { x: currentX, y: currentY },
    }));

    // 计算新的时间位置
    const newTime = calculateTimeFromPosition(currentX);
    
    // 检查智能吸附
    const snapResult = findNearestEnhancedSnapPoint(newTime, snapPoints, 200); // 200ms阈值
    
    setSnapState({
      isSnapped: snapResult.snapped,
      snapPoint: snapResult.snapPoint || null,
      snapResult,
    });

    // 计算插入位置
    const finalTime = snapResult.snapped ? snapResult.time : newTime;
    const insertX = calculateTimelinePosition(finalTime);
    
    setFeedbackState(prev => ({
      ...prev,
      insertPosition: {
        x: insertX,
        y: currentY,
        time: finalTime,
      },
      alignmentLines: snapResult.snapped ? {
        vertical: [finalTime],
        horizontal: [],
      } : null,
    }));

    // 检查是否为有效放置
    const isValid = finalTime >= 0 && finalTime <= timelineDisplayDuration;
    setDragState(prev => ({
      ...prev,
      isValidDrop: isValid,
    }));

  }, [dragState.isDragging, dragState.dragElement, calculateTimeFromPosition, calculateTimelinePosition, snapPoints, timelineDisplayDuration]);

  // 处理拖拽结束
  const handleDragEnd = useCallback((event: MouseEvent) => {
    if (!dragState.isDragging || !dragState.dragElement) return;

    const finalTime = feedbackState.insertPosition?.time || dragState.dragElement.timeFrame.start;
    
    // 应用最终位置
    if (dragState.isValidDrop && finalTime !== dragState.dragElement.timeFrame.start) {
      const duration = dragState.dragElement.timeFrame.end - dragState.dragElement.timeFrame.start;
      
      store.updateEditorElementTimeFrame(
        dragState.dragElement,
        { start: finalTime, end: finalTime + duration },
        true
      );
      
      // 修复重叠
      store.trackManager.fixTrackElementsOverlap(track.id);
      store.saveChange();
    }

    // 重置状态
    setDragState({
      isDragging: false,
      isResizing: false,
      dragElement: null,
      dragType: "move",
      targetTrack: null,
      isValidDrop: false,
    });

    setSnapState({
      isSnapped: false,
      snapPoint: null,
      snapResult: null,
    });

    setFeedbackState({
      insertPosition: null,
      alignmentLines: null,
      dragPosition: null,
    });

  }, [dragState, feedbackState.insertPosition, store, track.id]);

  // 动画完成回调
  const handleAnimationComplete = useCallback(() => {
    // 动画完成后的清理工作
    if (!dragState.isDragging) {
      setSnapState(prev => ({ ...prev, isSnapped: false }));
    }
  }, [dragState.isDragging]);

  // 组合样式
  const containerStyles = useMemo(() => ({
    position: "relative",
    width: "100%",
    height: "100%",
    overflow: "hidden",
    borderRadius: "6px",
    // 性能优化
    willChange: dragState.isDragging ? "transform" : "auto",
    contain: "layout style paint",
  }), [dragState.isDragging]);

  return (
    <Box ref={containerRef} sx={containerStyles}>
      {/* 性能优化的拖拽容器 */}
      <PerformanceOptimizedDrag
        onDragStart={(e) => {
          // 这里可以根据具体的元素来调用 handleDragStart
          // 实际实现中需要从事件中确定具体的元素和拖拽类型
        }}
        onDragMove={handleDragMove}
        onDragEnd={handleDragEnd}
        enableAutoScroll={dragConfig.enableAutoScroll}
        throttleMs={dragConfig.throttleMs}
        scrollContainer={containerRef.current}
        boundaryContainer={containerRef.current}
      >
        {/* 视觉反馈系统 */}
        <VisualFeedbackSystem
          isDragging={dragState.isDragging}
          dragElement={dragState.dragElement}
          targetTrack={dragState.targetTrack}
          isValidDrop={dragState.isValidDrop}
          snapPoint={snapState.snapPoint}
          dragPosition={feedbackState.dragPosition}
        >
          {/* 流畅动画系统 */}
          <SmoothDragAnimations
            isDragging={dragState.isDragging}
            isResizing={dragState.isResizing}
            isSnapped={snapState.isSnapped}
            dragType={dragState.dragType}
            onAnimationComplete={handleAnimationComplete}
          >
            {/* 主要内容 */}
            {children}
          </SmoothDragAnimations>
        </VisualFeedbackSystem>
      </PerformanceOptimizedDrag>

      {/* 拖拽指示器 */}
      <DragIndicators
        isDragging={dragState.isDragging}
        dragElement={dragState.dragElement}
        targetTrack={dragState.targetTrack}
        insertPosition={feedbackState.insertPosition}
        alignmentLines={feedbackState.alignmentLines}
        containerWidth={containerWidth}
        timelineDisplayDuration={timelineDisplayDuration}
        timelinePanOffsetX={timelinePanOffsetX}
      />

      {/* 增强的吸附指示器 */}
      {snapState.isSnapped && snapState.snapPoint && (
        <>
          <EnhancedSnapIndicator
            position="left"
            isVisible={true}
            snapPoint={snapState.snapPoint}
            animationDuration={200}
          />
          <EnhancedSnapIndicator
            position="right"
            isVisible={true}
            snapPoint={snapState.snapPoint}
            animationDuration={200}
          />
        </>
      )}
    </Box>
  );
};

export const ClipchampStyleTimelineManager = memo(
  observer(ClipchampStyleTimelineManagerComponent),
  (prevProps, nextProps) => {
    return (
      prevProps.track.id === nextProps.track.id &&
      prevProps.elements.length === nextProps.elements.length &&
      prevProps.containerWidth === nextProps.containerWidth &&
      prevProps.timelineDisplayDuration === nextProps.timelineDisplayDuration &&
      prevProps.timelinePanOffsetX === nextProps.timelinePanOffsetX &&
      prevProps.currentTime === nextProps.currentTime
    );
  }
);

ClipchampStyleTimelineManager.displayName = "ClipchampStyleTimelineManager";
