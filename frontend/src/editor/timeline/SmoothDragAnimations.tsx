import React, { memo, useMemo, useRef, useEffect, useState } from "react";
import { Box } from "@mui/material";

interface SmoothDragAnimationsProps {
  isDragging: boolean;
  isResizing: boolean;
  isSnapped: boolean;
  dragType?: "move" | "resize-left" | "resize-right";
  children: React.ReactNode;
  onAnimationComplete?: () => void;
}

/**
 * 流畅拖拽动画系统
 * 使用CSS3 transform和transition优化性能，实现60fps流畅动画
 */
const SmoothDragAnimationsComponent = ({
  isDragging,
  isResizing,
  isSnapped,
  dragType = "move",
  children,
  onAnimationComplete,
}: SmoothDragAnimationsProps) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [animationState, setAnimationState] = useState<"idle" | "dragging" | "snapping" | "releasing">("idle");
  const [lastTransform, setLastTransform] = useState("");

  // 动画状态管理
  useEffect(() => {
    if (isDragging || isResizing) {
      setAnimationState("dragging");
    } else if (isSnapped) {
      setAnimationState("snapping");
      // 吸附动画完成后回到idle状态
      const timer = setTimeout(() => {
        setAnimationState("idle");
        onAnimationComplete?.();
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setAnimationState("releasing");
      // 释放动画完成后回到idle状态
      const timer = setTimeout(() => {
        setAnimationState("idle");
        onAnimationComplete?.();
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [isDragging, isResizing, isSnapped, onAnimationComplete]);

  // 获取动画配置
  const getAnimationConfig = useMemo(() => {
    switch (animationState) {
      case "dragging":
        return {
          transition: "none", // 拖拽时禁用transition以获得即时响应
          willChange: "transform, box-shadow, border-color",
          transform: "translate3d(0, 0, 0)", // 启用硬件加速
        };
      case "snapping":
        return {
          transition: "all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)", // easeOutQuad
          willChange: "transform, box-shadow, border-color",
          transform: "translate3d(0, 0, 0) scale(1.02)", // 轻微放大表示吸附
        };
      case "releasing":
        return {
          transition: "all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
          willChange: "transform, box-shadow, border-color",
          transform: "translate3d(0, 0, 0) scale(1)", // 恢复原始大小
        };
      default:
        return {
          transition: "transform 0.15s ease-out, box-shadow 0.15s ease-out",
          willChange: "auto",
          transform: "translate3d(0, 0, 0)",
        };
    }
  }, [animationState]);

  // 拖拽类型特定样式
  const getDragTypeStyles = useMemo(() => {
    if (!isDragging && !isResizing) return {};

    const baseStyles = {
      zIndex: 1000,
      cursor: "grabbing",
      userSelect: "none",
      pointerEvents: "auto",
    };

    switch (dragType) {
      case "move":
        return {
          ...baseStyles,
          transform: "translate3d(0, 0, 0) scale(1.01) rotate(0.5deg)",
          boxShadow: `
            0 12px 40px rgba(0, 0, 0, 0.15),
            0 6px 20px rgba(0, 0, 0, 0.1),
            0 2px 8px rgba(0, 0, 0, 0.08)
          `,
          filter: "brightness(1.05) saturate(1.1)",
        };
      case "resize-left":
        return {
          ...baseStyles,
          cursor: "ew-resize",
          transformOrigin: "right center",
          transform: "translate3d(0, 0, 0) scaleX(1.02)",
          boxShadow: `
            -4px 0 12px rgba(33, 150, 243, 0.3),
            0 4px 16px rgba(0, 0, 0, 0.1)
          `,
        };
      case "resize-right":
        return {
          ...baseStyles,
          cursor: "ew-resize",
          transformOrigin: "left center",
          transform: "translate3d(0, 0, 0) scaleX(1.02)",
          boxShadow: `
            4px 0 12px rgba(33, 150, 243, 0.3),
            0 4px 16px rgba(0, 0, 0, 0.1)
          `,
        };
      default:
        return baseStyles;
    }
  }, [isDragging, isResizing, dragType]);

  // 吸附动画样式
  const getSnapStyles = useMemo(() => {
    if (!isSnapped) return {};

    return {
      borderColor: "#ff9800 !important",
      boxShadow: `
        0 0 20px rgba(255, 152, 0, 0.6),
        0 8px 32px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3)
      `,
      "&::before": {
        content: '""',
        position: "absolute",
        top: "-2px",
        left: "-2px",
        right: "-2px",
        bottom: "-2px",
        background: "linear-gradient(45deg, rgba(255, 152, 0, 0.3), transparent, rgba(255, 152, 0, 0.3))",
        borderRadius: "inherit",
        zIndex: -1,
        animation: "snapGlow 0.6s ease-out",
      },
      "@keyframes snapGlow": {
        "0%": {
          opacity: 0,
          transform: "scale(0.8)",
        },
        "50%": {
          opacity: 1,
          transform: "scale(1.1)",
        },
        "100%": {
          opacity: 0.7,
          transform: "scale(1)",
        },
      },
    };
  }, [isSnapped]);

  // 性能优化的样式组合
  const combinedStyles = useMemo(() => ({
    position: "relative",
    backfaceVisibility: "hidden",
    WebkitBackfaceVisibility: "hidden",
    perspective: "1000px",
    WebkitPerspective: "1000px",
    ...getAnimationConfig,
    ...getDragTypeStyles,
    ...getSnapStyles,
    // 添加微妙的边框动画
    ...(isDragging && {
      "&::after": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        border: "1px solid rgba(33, 150, 243, 0.5)",
        borderRadius: "inherit",
        animation: "borderFlow 2s linear infinite",
        pointerEvents: "none",
      },
      "@keyframes borderFlow": {
        "0%": {
          borderImageSource: "linear-gradient(0deg, rgba(33, 150, 243, 0.8), transparent)",
        },
        "25%": {
          borderImageSource: "linear-gradient(90deg, rgba(33, 150, 243, 0.8), transparent)",
        },
        "50%": {
          borderImageSource: "linear-gradient(180deg, rgba(33, 150, 243, 0.8), transparent)",
        },
        "75%": {
          borderImageSource: "linear-gradient(270deg, rgba(33, 150, 243, 0.8), transparent)",
        },
        "100%": {
          borderImageSource: "linear-gradient(360deg, rgba(33, 150, 243, 0.8), transparent)",
        },
      },
    }),
  }), [getAnimationConfig, getDragTypeStyles, getSnapStyles, isDragging]);

  // 使用 RAF 优化动画性能
  useEffect(() => {
    if (!elementRef.current) return;

    let rafId: number;
    
    const updateAnimation = () => {
      if (elementRef.current) {
        const currentTransform = elementRef.current.style.transform;
        if (currentTransform !== lastTransform) {
          setLastTransform(currentTransform);
        }
      }
      
      if (isDragging || isResizing) {
        rafId = requestAnimationFrame(updateAnimation);
      }
    };

    if (isDragging || isResizing) {
      rafId = requestAnimationFrame(updateAnimation);
    }

    return () => {
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [isDragging, isResizing, lastTransform]);

  // 添加触摸设备优化
  const touchOptimizations = useMemo(() => ({
    WebkitTouchCallout: "none",
    WebkitUserSelect: "none",
    KhtmlUserSelect: "none",
    MozUserSelect: "none",
    msUserSelect: "none",
    userSelect: "none",
    WebkitTapHighlightColor: "transparent",
    touchAction: isDragging ? "none" : "auto",
  }), [isDragging]);

  return (
    <Box
      ref={elementRef}
      sx={{
        ...combinedStyles,
        ...touchOptimizations,
      }}
      className={`
        smooth-drag-element
        ${animationState !== "idle" ? `animation-${animationState}` : ""}
        ${isDragging ? "dragging" : ""}
        ${isResizing ? "resizing" : ""}
        ${isSnapped ? "snapped" : ""}
      `}
    >
      {children}
    </Box>
  );
};

export const SmoothDragAnimations = memo(
  SmoothDragAnimationsComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.isDragging === nextProps.isDragging &&
      prevProps.isResizing === nextProps.isResizing &&
      prevProps.isSnapped === nextProps.isSnapped &&
      prevProps.dragType === nextProps.dragType
    );
  }
);

SmoothDragAnimations.displayName = "SmoothDragAnimations";
