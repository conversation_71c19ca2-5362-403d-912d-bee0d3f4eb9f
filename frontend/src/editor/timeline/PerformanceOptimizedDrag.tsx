import React, { memo, useCallback, useRef, useEffect, useMemo } from "react";
import { Box } from "@mui/material";

interface PerformanceOptimizedDragProps {
  children: React.ReactNode;
  onDragStart?: (event: React.MouseEvent) => void;
  onDragMove?: (event: MouseEvent, deltaX: number, deltaY: number) => void;
  onDragEnd?: (event: MouseEvent) => void;
  enableAutoScroll?: boolean;
  scrollContainer?: HTMLElement | null;
  boundaryContainer?: HTMLElement | null;
  throttleMs?: number;
}

/**
 * 性能优化的拖拽组件
 * 实现60fps流畅拖拽体验，包括边界检测和自动滚动
 */
const PerformanceOptimizedDragComponent = ({
  children,
  onDragStart,
  onDragMove,
  onDragEnd,
  enableAutoScroll = true,
  scrollContainer,
  boundaryContainer,
  throttleMs = 16, // ~60fps
}: PerformanceOptimizedDragProps) => {
  const dragRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const lastMoveTimeRef = useRef(0);
  const animationFrameRef = useRef<number>();
  const autoScrollIntervalRef = useRef<number>();

  // 拖拽状态
  const dragStateRef = useRef({
    startX: 0,
    startY: 0,
    lastX: 0,
    lastY: 0,
    deltaX: 0,
    deltaY: 0,
  });

  // 性能优化的节流函数
  const throttledMove = useCallback(
    (callback: Function, ...args: any[]) => {
      const now = performance.now();
      if (now - lastMoveTimeRef.current >= throttleMs) {
        lastMoveTimeRef.current = now;
        callback(...args);
      }
    },
    [throttleMs]
  );

  // 边界检测
  const checkBoundaries = useCallback(
    (x: number, y: number) => {
      if (!boundaryContainer || !dragRef.current) return { x, y };

      const containerRect = boundaryContainer.getBoundingClientRect();
      const elementRect = dragRef.current.getBoundingClientRect();

      const minX = containerRect.left;
      const maxX = containerRect.right - elementRect.width;
      const minY = containerRect.top;
      const maxY = containerRect.bottom - elementRect.height;

      return {
        x: Math.max(minX, Math.min(maxX, x)),
        y: Math.max(minY, Math.min(maxY, y)),
      };
    },
    [boundaryContainer]
  );

  // 自动滚动功能
  const handleAutoScroll = useCallback(
    (clientX: number, clientY: number) => {
      if (!enableAutoScroll || !scrollContainer) return;

      const container = scrollContainer;
      const rect = container.getBoundingClientRect();
      const scrollThreshold = 50; // 触发滚动的边界距离
      const scrollSpeed = 10; // 滚动速度

      let scrollX = 0;
      let scrollY = 0;

      // 水平滚动检测
      if (clientX < rect.left + scrollThreshold) {
        scrollX = -scrollSpeed;
      } else if (clientX > rect.right - scrollThreshold) {
        scrollX = scrollSpeed;
      }

      // 垂直滚动检测
      if (clientY < rect.top + scrollThreshold) {
        scrollY = -scrollSpeed;
      } else if (clientY > rect.bottom - scrollThreshold) {
        scrollY = scrollSpeed;
      }

      // 执行滚动
      if (scrollX !== 0 || scrollY !== 0) {
        if (!autoScrollIntervalRef.current) {
          autoScrollIntervalRef.current = window.setInterval(() => {
            container.scrollBy(scrollX, scrollY);
          }, 16); // ~60fps
        }
      } else {
        if (autoScrollIntervalRef.current) {
          clearInterval(autoScrollIntervalRef.current);
          autoScrollIntervalRef.current = undefined;
        }
      }
    },
    [enableAutoScroll, scrollContainer]
  );

  // 优化的鼠标移动处理
  const handleMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!isDraggingRef.current) return;

      // 使用 RAF 确保流畅的动画
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        const currentX = event.clientX;
        const currentY = event.clientY;

        const deltaX = currentX - dragStateRef.current.lastX;
        const deltaY = currentY - dragStateRef.current.lastY;

        // 更新拖拽状态
        dragStateRef.current.lastX = currentX;
        dragStateRef.current.lastY = currentY;
        dragStateRef.current.deltaX = deltaX;
        dragStateRef.current.deltaY = deltaY;

        // 边界检测
        const bounded = checkBoundaries(currentX, currentY);

        // 自动滚动检测
        handleAutoScroll(bounded.x, bounded.y);

        // 节流调用移动回调
        throttledMove(onDragMove, event, deltaX, deltaY);
      });
    },
    [onDragMove, checkBoundaries, handleAutoScroll, throttledMove]
  );

  // 鼠标释放处理
  const handleMouseUp = useCallback(
    (event: MouseEvent) => {
      if (!isDraggingRef.current) return;

      isDraggingRef.current = false;

      // 清理动画帧和自动滚动
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = undefined;
      }

      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
        autoScrollIntervalRef.current = undefined;
      }

      // 移除事件监听器
      document.removeEventListener("mousemove", handleMouseMove, {
        passive: false,
      } as any);
      document.removeEventListener("mouseup", handleMouseUp);

      // 恢复文档选择
      document.body.style.userSelect = "";
      document.body.style.pointerEvents = "";

      // 调用结束回调
      onDragEnd?.(event);
    },
    [onDragEnd, handleMouseMove]
  );

  // 鼠标按下处理
  const handleMouseDown = useCallback(
    (event: React.MouseEvent) => {
      // 只响应左键
      if (event.button !== 0) return;

      event.preventDefault();
      event.stopPropagation();

      isDraggingRef.current = true;

      // 初始化拖拽状态
      dragStateRef.current = {
        startX: event.clientX,
        startY: event.clientY,
        lastX: event.clientX,
        lastY: event.clientY,
        deltaX: 0,
        deltaY: 0,
      };

      // 禁用文档选择以提高性能
      document.body.style.userSelect = "none";
      document.body.style.pointerEvents = "none";

      // 添加事件监听器（使用 passive: false 确保可以阻止默认行为）
      document.addEventListener("mousemove", handleMouseMove, {
        passive: false,
      });
      document.addEventListener("mouseup", handleMouseUp);

      // 调用开始回调
      onDragStart?.(event);
    },
    [onDragStart, handleMouseMove, handleMouseUp]
  );

  // 清理函数
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
      // 确保清理事件监听器
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      // 恢复文档样式
      document.body.style.userSelect = "";
      document.body.style.pointerEvents = "";
    };
  }, [handleMouseMove, handleMouseUp]);

  // 性能优化的样式
  const optimizedStyles = useMemo(
    () => ({
      willChange: "transform",
      transform: "translate3d(0, 0, 0)", // 启用硬件加速
      backfaceVisibility: "hidden",
      WebkitBackfaceVisibility: "hidden",
      cursor: "grab",
      userSelect: "none",
      WebkitUserSelect: "none",
      touchAction: "none", // 防止触摸设备上的默认行为
      "&:active": {
        cursor: "grabbing",
      },
      // 添加性能提示
      contain: "layout style paint", // CSS Containment API
    }),
    []
  );

  return (
    <Box
      ref={dragRef}
      sx={optimizedStyles}
      onMouseDown={handleMouseDown}
      className="performance-optimized-drag"
    >
      {children}
    </Box>
  );
};

export const PerformanceOptimizedDrag = memo(
  PerformanceOptimizedDragComponent,
  (prevProps, nextProps) => {
    // 只比较关键属性以避免不必要的重渲染
    return (
      prevProps.enableAutoScroll === nextProps.enableAutoScroll &&
      prevProps.throttleMs === nextProps.throttleMs &&
      prevProps.scrollContainer === nextProps.scrollContainer &&
      prevProps.boundaryContainer === nextProps.boundaryContainer &&
      prevProps.onDragStart === nextProps.onDragStart &&
      prevProps.onDragMove === nextProps.onDragMove &&
      prevProps.onDragEnd === nextProps.onDragEnd
    );
  }
);

PerformanceOptimizedDrag.displayName = "PerformanceOptimizedDrag";

// 导出性能优化的 Hook
export const usePerformanceOptimizedDrag = (options: {
  throttleMs?: number;
  enableAutoScroll?: boolean;
}) => {
  const { throttleMs = 16, enableAutoScroll = true } = options;

  return useMemo(
    () => ({
      throttleMs,
      enableAutoScroll,
      // 可以添加更多优化选项
    }),
    [throttleMs, enableAutoScroll]
  );
};

// 导出拖拽边界检测工具
export const useDragBoundaries = (
  containerRef: React.RefObject<HTMLElement>
) => {
  return useCallback(
    (x: number, y: number, elementWidth: number, elementHeight: number) => {
      if (!containerRef.current) return { x, y };

      const containerRect = containerRef.current.getBoundingClientRect();

      const minX = containerRect.left;
      const maxX = containerRect.right - elementWidth;
      const minY = containerRect.top;
      const maxY = containerRect.bottom - elementHeight;

      return {
        x: Math.max(minX, Math.min(maxX, x)),
        y: Math.max(minY, Math.min(maxY, y)),
        isWithinBounds: x >= minX && x <= maxX && y >= minY && y <= maxY,
      };
    },
    [containerRef]
  );
};
