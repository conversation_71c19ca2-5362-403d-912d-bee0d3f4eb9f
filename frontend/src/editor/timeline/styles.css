/* 性能优化通用类 */
.gpu-accelerated {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 拖拽相关样式 - 优化性能 */
.handle-dragging {
  transform: translate3d(0, -50%, 0) scale(1.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  will-change: transform;
}

.snapped,
.snapped-start,
.snapped-end {
  border-color: #ff9800 !important;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5) !important;
  will-change: border-color, box-shadow;
}

.resize-active {
  z-index: 30 !important;
  outline: 1px dashed rgba(33, 150, 243, 0.8) !important;
  will-change: outline;
}

.dragging-active {
  z-index: 30 !important;
  cursor: grabbing !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  transform: translate3d(0, 0, 0) scale(1.01) !important;
  will-change: transform, box-shadow;
}

.panning-mode {
  background-color: rgba(255, 152, 0, 0.2) !important;
  border: 1px dashed #ff9800 !important;
  will-change: background-color, border;
}

/* 选中元素样式 */
.element-selected {
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  z-index: 20 !important;
  will-change: border, box-shadow;
}

/* 吸附指示器样式 */
.snap-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ff9800;
  border-radius: 2px;
  opacity: 0.8;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
  z-index: 30;
  will-change: opacity;
  pointer-events: none; /* 防止干扰鼠标事件 */
}

.snap-indicator-left {
  left: 0;
}

.snap-indicator-right {
  right: 0;
}

/* 基础类用于所有时间线元素 */
.timeline-element-container {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  pointer-events: auto;
  border-radius: 6px;
}

/* 性能优化容器类 */
.timeline-container,
.timeline-elements-container,
.timeline-elements-list {
  contain: content; /* 包含内容，提高性能 */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.timeline-container {
  -webkit-overflow-scrolling: touch; /* 在iOS上启用惯性滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
  border-radius: 8px;
}

.timeline-elements-container {
  contain: layout style; /* 包含布局和样式变化 */
  border-radius: 6px;
}

/* 轨道样式美化 */
.timeline-track {
  border-radius: 6px;
  margin: 4px 0;
  transition: background-color 0.2s ease;
}

/* 轨道标题美化 */
.track-title {
  font-weight: 500;
  color: #3a3a3a;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.03);
  margin-right: 8px;
}

/* 美化时间线元素 */
.timeline-element {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.timeline-element:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 时间线元素内容 */
.element-content {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 字幕元素样式美化 */
.caption-element {
  background-color: rgba(156, 39, 176, 0.8);
  border-radius: 6px;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
}

.caption-element:hover {
  background-color: rgba(156, 39, 176, 0.9);
  box-shadow: 0 3px 6px rgba(156, 39, 176, 0.4);
}

/* 控制把手美化 */
.control-handle {
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: transform 0.15s ease;
}

.control-handle:hover {
  transform: scale(1.2);
}

/* 右键菜单样式 */
.context-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.context-menu-item {
  padding: 10px 16px;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: rgba(33, 150, 243, 0.08);
}

/* Clipchamp风格的增强样式 */
.clipchamp-timeline-manager {
  position: relative;
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0);
}

/* 智能吸附增强样式 */
.enhanced-snap-active {
  position: relative;
  z-index: 1000;
}

.enhanced-snap-active::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid var(--snap-color, #ff9800);
  border-radius: 8px;
  box-shadow: 0 0 16px var(--snap-glow, rgba(255, 152, 0, 0.6));
  animation: enhancedSnapPulse 1.5s infinite;
  pointer-events: none;
}

@keyframes enhancedSnapPulse {
  0% {
    opacity: 0.6;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.98);
  }
}

/* 拖拽指示器样式 */
.drag-indicator-line {
  position: absolute;
  background: linear-gradient(45deg, #2196f3, #21cbf3);
  box-shadow: 0 0 12px rgba(33, 150, 243, 0.8);
  border-radius: 2px;
  animation: indicatorPulse 2s infinite;
  pointer-events: none;
  z-index: 100;
}

@keyframes indicatorPulse {
  0% {
    opacity: 0.7;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 16px rgba(33, 150, 243, 1);
  }
  100% {
    opacity: 0.7;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);
  }
}

/* 实时视觉反馈样式 */
.visual-feedback-active {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.visual-feedback-valid {
  background-color: rgba(76, 175, 80, 0.1) !important;
  border: 2px dashed rgba(76, 175, 80, 0.8) !important;
  box-shadow: inset 0 0 20px rgba(76, 175, 80, 0.3) !important;
}

.visual-feedback-invalid {
  background-color: rgba(244, 67, 54, 0.1) !important;
  border: 2px dashed rgba(244, 67, 54, 0.8) !important;
  animation: invalidShake 0.5s infinite;
}

@keyframes invalidShake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
  100% {
    transform: translateX(0);
  }
}

/* 流畅动画样式 */
.smooth-drag-element {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
  -webkit-perspective: 1000px;
}

.smooth-drag-element.animation-dragging {
  transition: none !important;
  will-change: transform, box-shadow, border-color;
}

.smooth-drag-element.animation-snapping {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translate3d(0, 0, 0) scale(1.02) !important;
}

.smooth-drag-element.animation-releasing {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translate3d(0, 0, 0) scale(1) !important;
}

/* 性能优化样式 */
.performance-optimized-drag {
  contain: layout style paint;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.performance-optimized-drag:active {
  cursor: grabbing !important;
}

/* 自动滚动指示器 */
.auto-scroll-indicator {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: radial-gradient(circle, #2196f3, #1976d2);
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.8);
  animation: autoScrollPulse 1s infinite;
  pointer-events: none;
  z-index: 10000;
}

@keyframes autoScrollPulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

/* 轨道性能优化 */
.timeline-track {
  will-change: transform, opacity;
  transform: translateZ(0);
  transition: opacity 0.2s ease;
}
