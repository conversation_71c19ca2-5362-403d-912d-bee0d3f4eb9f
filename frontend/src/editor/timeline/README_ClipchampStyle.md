# Clipchamp风格时间线轨道管理系统

这个系统实现了类似Clipchamp的专业级时间线轨道管理效果，包括智能吸附、实时视觉反馈、流畅动画过渡和性能优化。

## 功能特性

### 1. 智能吸附功能增强
- **多种吸附类型**：元素边缘、播放头位置、时间线标记、网格线
- **优先级系统**：播放头 > 元素边缘 > 时间线标记 > 网格线
- **智能阈值**：根据吸附类型动态调整吸附距离
- **视觉反馈**：不同吸附类型显示不同颜色和动画效果

### 2. 实时视觉反馈系统
- **拖拽状态指示**：高亮、阴影、缩放效果
- **轨道匹配提示**：有效/无效放置的视觉反馈
- **类型兼容性检查**：实时显示元素与轨道的兼容性
- **鼠标跟随效果**：拖拽时的光标增强效果

### 3. 流畅拖拽动画过渡
- **硬件加速**：使用CSS3 transform和GPU加速
- **状态动画**：拖拽、吸附、释放的流畅过渡
- **性能优化**：60fps流畅动画，避免重绘和回流
- **触摸设备优化**：支持移动设备的触摸操作

### 4. 拖拽指示器系统
- **插入位置指示**：显示元素将要放置的精确位置
- **对齐线**：垂直和水平对齐参考线
- **轨道高亮**：目标轨道的视觉高亮
- **元素预览**：拖拽时显示元素的半透明预览

### 5. 性能优化和用户体验
- **边界检测**：防止元素拖拽到无效区域
- **自动滚动**：拖拽到边缘时自动滚动时间线
- **节流优化**：16ms节流确保60fps性能
- **内存管理**：自动清理事件监听器和动画帧

## 组件架构

```
ClipchampStyleTimelineManager (主容器)
├── PerformanceOptimizedDrag (性能优化拖拽)
│   └── VisualFeedbackSystem (视觉反馈)
│       └── SmoothDragAnimations (流畅动画)
│           └── children (实际内容)
├── DragIndicators (拖拽指示器)
└── EnhancedSnapIndicator (增强吸附指示器)
```

## 使用方法

### 基本集成

```tsx
import { ClipchampStyleTimelineManager } from './ClipchampStyleTimelineManager';

// 在TrackView组件中使用
<ClipchampStyleTimelineManager
  track={track}
  elements={elements}
  containerWidth={containerWidth}
  timelineDisplayDuration={store.timelineDisplayDuration}
  timelinePanOffsetX={store.timelinePan.offsetX}
  currentTime={store.currentTime}
>
  {/* 原有的时间线元素内容 */}
  {elements.map((element) => (
    <TimeFrameView
      key={element.id}
      element={element}
      // ... 其他props
    />
  ))}
</ClipchampStyleTimelineManager>
```

### 独立组件使用

```tsx
// 只使用智能吸附
import { EnhancedSnapIndicator } from './EnhancedSnapIndicator';
import { generateEnhancedSnapPoints, findNearestEnhancedSnapPoint } from './utils';

// 只使用视觉反馈
import { VisualFeedbackSystem } from './VisualFeedbackSystem';

// 只使用性能优化拖拽
import { PerformanceOptimizedDrag } from './PerformanceOptimizedDrag';
```

### 自定义配置

```tsx
// 自定义吸附阈值和类型
const customSnapPoints = generateEnhancedSnapPoints(
  elements,
  currentTime,
  timelineDisplayDuration,
  excludeElementId
);

// 自定义性能配置
const dragConfig = usePerformanceOptimizedDrag({
  throttleMs: 8, // 更高的帧率
  enableAutoScroll: false, // 禁用自动滚动
});
```

## CSS类名和样式

### 主要CSS类
- `.clipchamp-timeline-manager` - 主容器样式
- `.enhanced-snap-active` - 吸附激活状态
- `.visual-feedback-valid` - 有效放置反馈
- `.visual-feedback-invalid` - 无效放置反馈
- `.smooth-drag-element` - 流畅动画元素
- `.performance-optimized-drag` - 性能优化拖拽

### 自定义CSS变量
```css
:root {
  --snap-color: #ff9800;
  --snap-glow: rgba(255, 152, 0, 0.6);
  --valid-color: #4caf50;
  --invalid-color: #f44336;
  --drag-color: #2196f3;
}
```

## 性能考虑

1. **硬件加速**：所有动画使用`transform3d`启用GPU加速
2. **事件节流**：鼠标移动事件16ms节流，确保60fps
3. **内存管理**：自动清理RAF和事件监听器
4. **CSS Containment**：使用`contain`属性优化重绘
5. **Will-change**：合理使用`will-change`提示浏览器优化

## 浏览器兼容性

- **现代浏览器**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **移动设备**：iOS Safari 12+, Chrome Mobile 60+
- **特性检测**：自动降级不支持的特性

## 故障排除

### 常见问题

1. **动画卡顿**：检查是否启用了硬件加速，确保CSS中有`transform3d`
2. **吸附不准确**：调整吸附阈值，检查时间线计算逻辑
3. **内存泄漏**：确保组件卸载时清理了所有事件监听器
4. **触摸设备问题**：检查`touch-action`和`user-select`设置

### 调试工具

```tsx
// 启用调试模式
const DEBUG_MODE = process.env.NODE_ENV === 'development';

// 在组件中添加调试信息
{DEBUG_MODE && (
  <div style={{ position: 'fixed', top: 0, left: 0, background: 'white', padding: '10px' }}>
    <div>拖拽状态: {isDragging ? '是' : '否'}</div>
    <div>吸附状态: {isSnapped ? '是' : '否'}</div>
    <div>当前时间: {currentTime}ms</div>
  </div>
)}
```

## 扩展和自定义

### 添加新的吸附类型

```tsx
// 在utils.ts中扩展SnapType枚举
export enum SnapType {
  // ... 现有类型
  CUSTOM_MARKER = 'custom_marker',
}

// 在generateEnhancedSnapPoints中添加逻辑
snapPoints.push({
  time: customMarkerTime,
  type: SnapType.CUSTOM_MARKER,
  priority: 1.5, // 自定义优先级
});
```

### 自定义视觉效果

```tsx
// 扩展VisualFeedbackSystem组件
const customFeedbackColors = {
  primary: "#your-color",
  secondary: "rgba(your-color, 0.2)",
  // ...
};
```

这个系统提供了完整的Clipchamp风格时间线体验，可以根据具体需求进行定制和扩展。
