import { Box } from "@mui/material";
import React, { memo, useMemo } from "react";
import { EditorElement, Track } from "../../types";

interface DragIndicatorsProps {
  isDragging: boolean;
  dragElement?: EditorElement;
  targetTrack?: Track;
  insertPosition?: {
    x: number;
    y: number;
    time: number;
  };
  alignmentLines?: {
    vertical: number[];
    horizontal: number[];
  };
  containerWidth: number;
  timelineDisplayDuration: number;
  timelinePanOffsetX: number;
}

/**
 * 拖拽指示器系统组件
 * 提供拖拽时的视觉反馈，包括插入位置指示器、对齐线等
 */
const DragIndicatorsComponent = ({
  isDragging,
  dragElement,
  targetTrack,
  insertPosition,
  alignmentLines,
  containerWidth,
  timelineDisplayDuration,
  timelinePanOffsetX,
}: DragIndicatorsProps) => {
  // 计算时间线位置
  const calculateTimelinePosition = useMemo(() => {
    return (time: number): number => {
      const relativeTime = time - timelinePanOffsetX;
      return (relativeTime / timelineDisplayDuration) * containerWidth;
    };
  }, [timelineDisplayDuration, timelinePanOffsetX, containerWidth]);

  // 插入位置指示器样式
  const insertIndicatorStyles = useMemo(() => {
    if (!insertPosition || !isDragging) return { display: "none" };

    const xPosition = calculateTimelinePosition(insertPosition.time);

    return {
      position: "absolute",
      left: `${xPosition}px`,
      top: 0,
      bottom: 0,
      width: "3px",
      backgroundColor: "#2196f3",
      borderRadius: "2px",
      zIndex: 100,
      boxShadow: "0 0 12px rgba(33, 150, 243, 0.8)",
      opacity: 0.9,
      animation: "pulseInsert 1.5s infinite",
      willChange: "opacity, transform",
      "&::before": {
        content: '""',
        position: "absolute",
        top: "-8px",
        left: "50%",
        transform: "translateX(-50%)",
        width: 0,
        height: 0,
        borderLeft: "6px solid transparent",
        borderRight: "6px solid transparent",
        borderBottom: "8px solid #2196f3",
        filter: "drop-shadow(0 2px 4px rgba(33, 150, 243, 0.5))",
      },
      "&::after": {
        content: '""',
        position: "absolute",
        bottom: "-8px",
        left: "50%",
        transform: "translateX(-50%)",
        width: 0,
        height: 0,
        borderLeft: "6px solid transparent",
        borderRight: "6px solid transparent",
        borderTop: "8px solid #2196f3",
        filter: "drop-shadow(0 -2px 4px rgba(33, 150, 243, 0.5))",
      },
      "@keyframes pulseInsert": {
        "0%": { 
          opacity: 0.7,
          transform: "scaleY(1)",
        },
        "50%": { 
          opacity: 1,
          transform: "scaleY(1.05)",
        },
        "100%": { 
          opacity: 0.7,
          transform: "scaleY(1)",
        },
      },
    };
  }, [insertPosition, isDragging, calculateTimelinePosition]);

  // 垂直对齐线样式
  const verticalAlignmentStyles = useMemo(() => {
    if (!alignmentLines?.vertical || !isDragging) return [];

    return alignmentLines.vertical.map((time, index) => {
      const xPosition = calculateTimelinePosition(time);
      
      return {
        key: `vertical-${index}`,
        sx: {
          position: "absolute",
          left: `${xPosition}px`,
          top: 0,
          bottom: 0,
          width: "1px",
          backgroundColor: "#ff9800",
          opacity: 0.8,
          zIndex: 90,
          animation: "fadeInOut 2s infinite",
          willChange: "opacity",
          "@keyframes fadeInOut": {
            "0%": { opacity: 0.5 },
            "50%": { opacity: 1 },
            "100%": { opacity: 0.5 },
          },
        },
      };
    });
  }, [alignmentLines?.vertical, isDragging, calculateTimelinePosition]);

  // 水平对齐线样式
  const horizontalAlignmentStyles = useMemo(() => {
    if (!alignmentLines?.horizontal || !isDragging) return [];

    return alignmentLines.horizontal.map((yPosition, index) => ({
      key: `horizontal-${index}`,
      sx: {
        position: "absolute",
        left: 0,
        right: 0,
        top: `${yPosition}px`,
        height: "1px",
        backgroundColor: "#ff9800",
        opacity: 0.8,
        zIndex: 90,
        animation: "fadeInOut 2s infinite",
        willChange: "opacity",
        "@keyframes fadeInOut": {
          "0%": { opacity: 0.5 },
          "50%": { opacity: 1 },
          "100%": { opacity: 0.5 },
        },
      },
    }));
  }, [alignmentLines?.horizontal, isDragging]);

  // 轨道高亮指示器样式
  const trackHighlightStyles = useMemo(() => {
    if (!targetTrack || !isDragging) return { display: "none" };

    return {
      position: "absolute",
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      backgroundColor: "rgba(33, 150, 243, 0.1)",
      border: "2px dashed rgba(33, 150, 243, 0.6)",
      borderRadius: "6px",
      zIndex: 80,
      animation: "pulseTrack 2s infinite",
      willChange: "opacity, transform",
      "@keyframes pulseTrack": {
        "0%": { 
          opacity: 0.6,
          transform: "scale(1)",
        },
        "50%": { 
          opacity: 0.9,
          transform: "scale(1.01)",
        },
        "100%": { 
          opacity: 0.6,
          transform: "scale(1)",
        },
      },
    };
  }, [targetTrack, isDragging]);

  // 拖拽元素预览样式
  const dragPreviewStyles = useMemo(() => {
    if (!dragElement || !insertPosition || !isDragging) return { display: "none" };

    const xPosition = calculateTimelinePosition(insertPosition.time);
    const elementDuration = dragElement.timeFrame.end - dragElement.timeFrame.start;
    const elementWidth = (elementDuration / timelineDisplayDuration) * containerWidth;

    return {
      position: "absolute",
      left: `${xPosition}px`,
      top: "2px",
      width: `${elementWidth}px`,
      height: "calc(100% - 4px)",
      backgroundColor: "rgba(33, 150, 243, 0.3)",
      border: "2px dashed rgba(33, 150, 243, 0.8)",
      borderRadius: "4px",
      zIndex: 95,
      opacity: 0.7,
      animation: "ghostElement 1.5s infinite",
      willChange: "opacity",
      "@keyframes ghostElement": {
        "0%": { opacity: 0.5 },
        "50%": { opacity: 0.8 },
        "100%": { opacity: 0.5 },
      },
    };
  }, [dragElement, insertPosition, isDragging, calculateTimelinePosition, timelineDisplayDuration, containerWidth]);

  if (!isDragging) return null;

  return (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: "none",
        zIndex: 75,
        overflow: "hidden",
      }}
    >
      {/* 轨道高亮指示器 */}
      <Box sx={trackHighlightStyles} />

      {/* 插入位置指示器 */}
      <Box sx={insertIndicatorStyles} />

      {/* 拖拽元素预览 */}
      <Box sx={dragPreviewStyles} />

      {/* 垂直对齐线 */}
      {verticalAlignmentStyles.map((style) => (
        <Box key={style.key} sx={style.sx} />
      ))}

      {/* 水平对齐线 */}
      {horizontalAlignmentStyles.map((style) => (
        <Box key={style.key} sx={style.sx} />
      ))}
    </Box>
  );
};

export const DragIndicators = memo(
  DragIndicatorsComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.isDragging === nextProps.isDragging &&
      prevProps.dragElement?.id === nextProps.dragElement?.id &&
      prevProps.targetTrack?.id === nextProps.targetTrack?.id &&
      prevProps.insertPosition?.time === nextProps.insertPosition?.time &&
      prevProps.insertPosition?.x === nextProps.insertPosition?.x &&
      prevProps.insertPosition?.y === nextProps.insertPosition?.y &&
      prevProps.containerWidth === nextProps.containerWidth &&
      prevProps.timelineDisplayDuration === nextProps.timelineDisplayDuration &&
      prevProps.timelinePanOffsetX === nextProps.timelinePanOffsetX &&
      JSON.stringify(prevProps.alignmentLines) === JSON.stringify(nextProps.alignmentLines)
    );
  }
);

DragIndicators.displayName = "DragIndicators";
