import { Box } from "@mui/material";
import React, { memo, useMemo } from "react";
import { SnapType, SnapPoint } from "./utils";

interface EnhancedSnapIndicatorProps {
  position: "left" | "right";
  isVisible: boolean;
  snapPoint?: SnapPoint;
  animationDuration?: number;
}

/**
 * 增强的吸附指示器组件
 * 根据吸附类型显示不同的视觉效果
 */
const EnhancedSnapIndicatorComponent = ({
  position,
  isVisible,
  snapPoint,
  animationDuration = 200,
}: EnhancedSnapIndicatorProps) => {
  // 根据吸附类型获取颜色和样式
  const getSnapStyles = useMemo(() => {
    if (!snapPoint) {
      return {
        color: "#ff9800",
        glowColor: "rgba(255, 152, 0, 0.7)",
        height: "100%",
        width: "3px",
        borderRadius: "2px",
      };
    }

    switch (snapPoint.type) {
      case SnapType.PLAYHEAD:
        return {
          color: "#f44336", // 红色 - 播放头
          glowColor: "rgba(244, 67, 54, 0.8)",
          height: "120%",
          width: "4px",
          borderRadius: "3px",
        };
      case SnapType.ELEMENT_START:
      case SnapType.ELEMENT_END:
        return {
          color: "#2196f3", // 蓝色 - 元素边缘
          glowColor: "rgba(33, 150, 243, 0.7)",
          height: "100%",
          width: "3px",
          borderRadius: "2px",
        };
      case SnapType.TIMELINE_MARKER:
        return {
          color: "#4caf50", // 绿色 - 时间线标记
          glowColor: "rgba(76, 175, 80, 0.7)",
          height: "110%",
          width: "3px",
          borderRadius: "2px",
        };
      case SnapType.GRID_LINE:
        return {
          color: "#9e9e9e", // 灰色 - 网格线
          glowColor: "rgba(158, 158, 158, 0.5)",
          height: "80%",
          width: "2px",
          borderRadius: "1px",
        };
      default:
        return {
          color: "#ff9800",
          glowColor: "rgba(255, 152, 0, 0.7)",
          height: "100%",
          width: "3px",
          borderRadius: "2px",
        };
    }
  }, [snapPoint]);

  // 优化样式对象的创建
  const indicatorStyles = useMemo(
    () => ({
      position: "absolute",
      [position]: 0,
      top: "50%",
      transform: "translateY(-50%)",
      width: getSnapStyles.width,
      height: getSnapStyles.height,
      bgcolor: getSnapStyles.color,
      borderRadius: getSnapStyles.borderRadius,
      zIndex: 60,
      boxShadow: `0 0 8px ${getSnapStyles.glowColor}`,
      opacity: isVisible ? 1 : 0,
      transition: `all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
      willChange: "opacity, transform",
      "&::before": {
        content: '""',
        position: "absolute",
        top: "50%",
        [position === "left" ? "left" : "right"]: "-4px",
        transform: "translateY(-50%)",
        width: "8px",
        height: "8px",
        borderRadius: "50%",
        bgcolor: getSnapStyles.color,
        boxShadow: `0 0 6px ${getSnapStyles.glowColor}`,
        opacity: snapPoint?.type === SnapType.PLAYHEAD ? 1 : 0.8,
      },
      "&::after": snapPoint?.type === SnapType.PLAYHEAD ? {
        content: '""',
        position: "absolute",
        top: "50%",
        [position === "left" ? "left" : "right"]: "-2px",
        transform: "translateY(-50%)",
        width: "4px",
        height: "4px",
        borderRadius: "50%",
        bgcolor: "#ffffff",
        zIndex: 1,
      } : {},
      // 根据吸附类型添加不同的动画效果
      animation: isVisible ? getAnimationName(snapPoint?.type) : "none",
      "@keyframes pulsePlayhead": {
        "0%": { 
          opacity: 0.8,
          transform: "translateY(-50%) scale(1)",
          boxShadow: `0 0 8px ${getSnapStyles.glowColor}`,
        },
        "50%": { 
          opacity: 1,
          transform: "translateY(-50%) scale(1.1)",
          boxShadow: `0 0 12px ${getSnapStyles.glowColor}`,
        },
        "100%": { 
          opacity: 0.8,
          transform: "translateY(-50%) scale(1)",
          boxShadow: `0 0 8px ${getSnapStyles.glowColor}`,
        },
      },
      "@keyframes pulseElement": {
        "0%": { opacity: 0.7 },
        "50%": { opacity: 1 },
        "100%": { opacity: 0.7 },
      },
      "@keyframes pulseTimeline": {
        "0%": { 
          opacity: 0.6,
          transform: "translateY(-50%) scale(0.9)",
        },
        "50%": { 
          opacity: 1,
          transform: "translateY(-50%) scale(1.05)",
        },
        "100%": { 
          opacity: 0.6,
          transform: "translateY(-50%) scale(0.9)",
        },
      },
      "@keyframes fadeGrid": {
        "0%": { opacity: 0.3 },
        "50%": { opacity: 0.6 },
        "100%": { opacity: 0.3 },
      },
    }),
    [position, isVisible, getSnapStyles, snapPoint, animationDuration]
  );

  // 获取动画名称
  function getAnimationName(snapType?: SnapType): string {
    if (!snapType) return "pulseElement 1.5s infinite";
    
    switch (snapType) {
      case SnapType.PLAYHEAD:
        return "pulsePlayhead 1s infinite";
      case SnapType.ELEMENT_START:
      case SnapType.ELEMENT_END:
        return "pulseElement 1.5s infinite";
      case SnapType.TIMELINE_MARKER:
        return "pulseTimeline 2s infinite";
      case SnapType.GRID_LINE:
        return "fadeGrid 2.5s infinite";
      default:
        return "pulseElement 1.5s infinite";
    }
  }

  if (!isVisible) return null;

  return <Box sx={indicatorStyles} />;
};

export const EnhancedSnapIndicator = memo(
  EnhancedSnapIndicatorComponent,
  (prevProps, nextProps) => {
    // 精确比较props，避免不必要的重渲染
    return (
      prevProps.position === nextProps.position &&
      prevProps.isVisible === nextProps.isVisible &&
      prevProps.snapPoint?.type === nextProps.snapPoint?.type &&
      prevProps.snapPoint?.time === nextProps.snapPoint?.time &&
      prevProps.animationDuration === nextProps.animationDuration
    );
  }
);

EnhancedSnapIndicator.displayName = "EnhancedSnapIndicator";
